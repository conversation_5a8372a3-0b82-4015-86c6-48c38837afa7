#!/bin/bash

# Safe VPN Bypass Solution
# NEVER touches VPN routes - uses completely isolated network namespace
# Maintains full VPN protection for all other system traffic

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Check VPN status
check_vpn_status() {
    if ! ip link show amn0 &>/dev/null; then
        log_warning "Amnezia VPN interface 'amn0' not found"
        log_info "This script is designed to work alongside Amnezia VPN"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "Amnezia VPN is active (amn0 interface found)"
    fi
}

# Detect physical network interface (the one used before VPN)
detect_physical_interface() {
    # Find the interface used for the VPN server connection
    VPN_SERVER_ROUTE=$(ip route show | grep "************" | head -1)
    if [[ -n "$VPN_SERVER_ROUTE" ]]; then
        PHYSICAL_IF=$(echo "$VPN_SERVER_ROUTE" | awk '{print $5}')
        PHYSICAL_GW=$(echo "$VPN_SERVER_ROUTE" | awk '{print $3}')
    else
        # Fallback: find non-VPN default route
        DEFAULT_ROUTE=$(ip route show | grep "default" | grep -v "amn0" | head -1)
        PHYSICAL_IF=$(echo "$DEFAULT_ROUTE" | awk '{print $5}')
        PHYSICAL_GW=$(echo "$DEFAULT_ROUTE" | awk '{print $3}')
    fi
    
    if [[ -z "$PHYSICAL_IF" || -z "$PHYSICAL_GW" ]]; then
        log_error "Could not detect physical interface and gateway"
        return 1
    fi
    
    PHYSICAL_IP=$(ip addr show "$PHYSICAL_IF" | grep "inet " | awk '{print $2}' | cut -d'/' -f1)
    
    if [[ -z "$PHYSICAL_IP" ]]; then
        log_error "Could not detect physical IP"
        return 1
    fi
    
    log_info "Physical network: $PHYSICAL_IF ($PHYSICAL_IP) via $PHYSICAL_GW"
    return 0
}

# Create isolated network namespace with direct internet access
create_bypass_namespace() {
    local ns_name="$1"
    local command="$2"
    shift 2
    local args="$*"
    
    # Detect physical network
    if ! detect_physical_interface; then
        return 1
    fi
    
    # Create namespace
    ip netns add "$ns_name" || return 1
    
    # Create veth pair
    ip link add "veth0" type veth peer name "veth1" || return 1
    
    # Move one end to namespace
    ip link set "veth1" netns "$ns_name" || return 1
    
    # Configure host side
    ip addr add "**********/24" dev "veth0" || return 1
    ip link set "veth0" up || return 1
    
    # Configure namespace side
    ip netns exec "$ns_name" ip addr add "**********/24" dev "veth1" || return 1
    ip netns exec "$ns_name" ip link set "veth1" up || return 1
    ip netns exec "$ns_name" ip link set lo up || return 1
    
    # Add default route in namespace
    ip netns exec "$ns_name" ip route add default via "**********" || return 1
    
    # Enable forwarding
    echo 1 > /proc/sys/net/ipv4/ip_forward
    
    # Set up NAT for namespace traffic to go directly through physical interface
    iptables -t nat -A POSTROUTING -s "**********/24" -o "$PHYSICAL_IF" -j MASQUERADE || return 1
    iptables -A FORWARD -i "veth0" -o "$PHYSICAL_IF" -j ACCEPT || return 1
    iptables -A FORWARD -i "$PHYSICAL_IF" -o "veth0" -m state --state RELATED,ESTABLISHED -j ACCEPT || return 1
    
    # Set up DNS in namespace
    mkdir -p "/etc/netns/$ns_name"
    echo "nameserver *******" > "/etc/netns/$ns_name/resolv.conf"
    echo "nameserver *******" >> "/etc/netns/$ns_name/resolv.conf"
    
    return 0
}

# Clean up namespace
cleanup_namespace() {
    local ns_name="$1"
    
    # Remove iptables rules
    iptables -t nat -D POSTROUTING -s "**********/24" -o "$PHYSICAL_IF" -j MASQUERADE 2>/dev/null || true
    iptables -D FORWARD -i "veth0" -o "$PHYSICAL_IF" -j ACCEPT 2>/dev/null || true
    iptables -D FORWARD -i "$PHYSICAL_IF" -o "veth0" -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
    
    # Remove veth interface (this also removes the namespace end)
    ip link delete "veth0" 2>/dev/null || true
    
    # Remove namespace
    ip netns delete "$ns_name" 2>/dev/null || true
    
    # Remove namespace config
    rm -rf "/etc/netns/$ns_name" 2>/dev/null || true
}

# Run command with VPN bypass
run_bypass_command() {
    local app_command="$*"
    
    if [[ -z "$app_command" ]]; then
        log_error "No command provided"
        return 1
    fi
    
    local ns_name="bypass_$$"
    
    # Set up cleanup trap
    trap "cleanup_namespace $ns_name" EXIT
    
    # Create the bypass namespace
    if ! create_bypass_namespace "$ns_name" "$@"; then
        log_error "Failed to create bypass namespace"
        return 1
    fi
    
    # Run the command in the namespace as the original user
    ip netns exec "$ns_name" sudo -u "$SUDO_USER" "$@"
    local exit_code=$?
    
    # Cleanup will be handled by trap
    return $exit_code
}

# Test the bypass functionality
test_bypass() {
    log_info "Testing bypass functionality..."
    
    # Test main system (should use VPN)
    log_info "Testing main system IP (should be VPN)..."
    MAIN_IP=$(curl -s --connect-timeout 10 ifconfig.me 2>/dev/null || echo "Failed")
    log_info "Main system IP: $MAIN_IP"
    
    # Test bypass (should use direct internet)
    log_info "Testing bypass IP (should be direct)..."
    BYPASS_IP=$(run_bypass_command curl -s --connect-timeout 10 ifconfig.me 2>/dev/null || echo "Failed")
    log_info "Bypass IP: $BYPASS_IP"
    
    if [[ "$BYPASS_IP" != "$MAIN_IP" && "$BYPASS_IP" != "Failed" && "$MAIN_IP" != "Failed" ]]; then
        log_success "SUCCESS: Bypass working!"
        log_success "  Main system (VPN): $MAIN_IP"
        log_success "  Bypass (direct): $BYPASS_IP"
        return 0
    else
        log_error "Bypass test failed"
        log_error "  Main system IP: $MAIN_IP"
        log_error "  Bypass IP: $BYPASS_IP"
        return 1
    fi
}

# Launch application with bypass
launch_app() {
    local app_command="$*"
    
    if [[ -z "$app_command" ]]; then
        log_error "No application command provided"
        echo ""
        echo "Usage: $0 <command> [args...]"
        echo ""
        echo "Examples:"
        echo "  $0 curl ifconfig.me"
        echo "  $0 wget https://example.com/file.zip"
        echo "  $0 firefox"
        echo "  $0 ping -c 3 google.com"
        echo ""
        exit 1
    fi
    
    log_info "Launching application with VPN bypass: $app_command"
    
    # Run the command with bypass
    run_bypass_command "$@"
}

# Show status
show_status() {
    log_info "Current network status:"
    
    # Show VPN interface
    if ip link show amn0 &>/dev/null; then
        VPN_IP=$(ip addr show amn0 | grep "inet " | awk '{print $2}' | cut -d'/' -f1 2>/dev/null || echo "Unknown")
        log_info "  VPN interface: amn0 ($VPN_IP)"
    else
        log_warning "  VPN interface: Not found"
    fi
    
    # Show VPN routes (count only, don't modify)
    VPN_ROUTES=$(ip route show | grep "dev amn0" | wc -l)
    log_info "  VPN routes: $VPN_ROUTES active"
    
    # Show external IP
    EXTERNAL_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || echo "Failed")
    log_info "  External IP: $EXTERNAL_IP"
    
    # Test if bypass would work
    log_info ""
    log_info "Testing bypass functionality..."
    if test_bypass; then
        log_success "Bypass is working correctly!"
    else
        log_error "Bypass test failed!"
    fi
}

# Main execution
main() {
    check_root
    check_vpn_status
    
    if [[ $# -eq 0 ]]; then
        echo ""
        echo "=== Safe VPN Bypass Solution ==="
        echo ""
        echo "This script allows specific applications to bypass the VPN"
        echo "while maintaining VPN protection for all other traffic."
        echo "It NEVER modifies VPN routes - uses isolated network namespaces."
        echo ""
        
        show_status
        
        echo ""
        echo "Usage: $0 <command> [args...]"
        echo ""
        echo "Examples:"
        echo "  $0 curl ifconfig.me          # Check real IP"
        echo "  $0 wget https://file.zip     # Download without VPN"
        echo "  $0 firefox                   # Browse with direct connection"
        echo "  $0 ping -c 3 google.com     # Ping without VPN"
        echo ""
        echo "The VPN will remain active for all other applications."
        echo ""
        exit 0
    fi
    
    launch_app "$@"
}

# Handle command line arguments
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
