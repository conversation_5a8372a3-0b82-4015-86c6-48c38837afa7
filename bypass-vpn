#!/bin/bash

# Simple wrapper script for VPN bypass
# Usage: bypass-vpn <command> [args...]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BYPASS_SCRIPT="$SCRIPT_DIR/concurrent_bypass_final.sh"

if [[ ! -f "$BYPASS_SCRIPT" ]]; then
    echo "Error: Bypass script not found at $BYPASS_SCRIPT"
    exit 1
fi

if [[ $# -eq 0 ]]; then
    echo "VPN Bypass Tool"
    echo "Usage: $0 <command> [args...]"
    echo ""
    echo "Examples:"
    echo "  $0 firefox                    # Launch Firefox with direct internet"
    echo "  $0 curl ifconfig.me          # Check your real IP address"
    echo "  $0 wget https://example.com  # Download without VPN"
    echo "  $0 bash                      # Start a shell with direct internet"
    echo ""
    echo "This tool uses network namespaces to allow ANY application to bypass"
    echo "Amnezia VPN while keeping the VPN active for all other applications."
    exit 1
fi

# Check if Amnezia VPN is running
if ! pgrep -f "amn0" >/dev/null; then
    echo "Warning: Amnezia VPN doesn't appear to be running"
    echo "VPN interface 'amn0' not found in running processes"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Run the bypass script with sudo
exec pkexec "$BYPASS_SCRIPT" "$@"
