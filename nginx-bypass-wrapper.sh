#!/bin/bash

# Wrapper script for nginx with VPN bypass for systemd service
# This script sets the required environment variables for concurrent_bypass_final.sh

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BYPASS_SCRIPT="$SCRIPT_DIR/concurrent_bypass_final.sh"

# Set the original user for the bypass script
# For system services, we run as root to avoid user environment issues
export SUDO_USER="root"

# Check if bypass script exists
if [[ ! -f "$BYPASS_SCRIPT" ]]; then
    echo "Error: Bypass script not found at $BYPASS_SCRIPT"
    exit 1
fi

# Execute the bypass script with all arguments
exec "$BYPASS_SCRIPT" "$@"
