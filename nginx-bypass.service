[Unit]
Description=The nginx HTTP and reverse proxy server (VPN Bypass)
After=network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Service]
Type=forking
PIDFile=/run/nginx.pid
# Nginx will fail to start if /run/nginx.pid already exists but has the wrong
# SELinux context. This might happen when running `nginx -t` from the cmdline.
# https://bugzilla.redhat.com/show_bug.cgi?id=1268621
ExecStartPre=/usr/bin/rm -f /run/nginx.pid
ExecStartPre=/home/<USER>/Documents/augment-projects/vpn_passthrough/concurrent_bypass_final.sh /usr/sbin/nginx -t
ExecStart=/home/<USER>/Documents/augment-projects/vpn_passthrough/concurrent_bypass_final.sh /usr/sbin/nginx
ExecReload=/home/<USER>/Documents/augment-projects/vpn_passthrough/concurrent_bypass_final.sh /usr/sbin/nginx -s reload
KillSignal=SIGQUIT
TimeoutStopSec=5
KillMode=mixed
PrivateTmp=true

[Install]
WantedBy=multi-user.target
