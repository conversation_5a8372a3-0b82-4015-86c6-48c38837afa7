#!/bin/bash

# Nginx VPN Bypass using Policy-Based Routing
# This script configures routing to bypass VPN for nginx outgoing connections
# while keeping nginx accessible from the main network

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root"
    exit 1
fi

# Detect physical network interface
detect_physical_network() {
    # Find the physical interface (not VPN)
    PHYSICAL_IF=$(ip route | grep default | grep -v amn | head -1 | awk '{print $5}')
    if [[ -z "$PHYSICAL_IF" ]]; then
        log_error "Could not detect physical network interface"
        return 1
    fi
    
    PHYSICAL_GW=$(ip route | grep default | grep "$PHYSICAL_IF" | awk '{print $3}')
    if [[ -z "$PHYSICAL_GW" ]]; then
        log_error "Could not detect physical gateway"
        return 1
    fi
    
    log_info "Physical network: $PHYSICAL_IF via $PHYSICAL_GW"
    return 0
}

# Setup routing for nginx bypass
setup_nginx_routing() {
    log_info "Setting up nginx VPN bypass routing..."

    # Create custom routing table for nginx
    if ! grep -q "200 nginx_bypass" /etc/iproute2/rt_tables; then
        echo "200 nginx_bypass" >> /etc/iproute2/rt_tables
    fi

    # Add routes to nginx_bypass table
    ip route add default via "$PHYSICAL_GW" dev "$PHYSICAL_IF" table nginx_bypass 2>/dev/null || true
    ip route add ***********/24 dev "$PHYSICAL_IF" table nginx_bypass 2>/dev/null || true

    # Get local IP address
    LOCAL_IP=$(ip route get ******* | grep -oP 'src \K\S+' | head -1)

    # Critical: Ensure responses to incoming connections go back through physical interface
    # This prevents VPN from intercepting response packets
    ip rule add from "$LOCAL_IP" table nginx_bypass priority 100 2>/dev/null || true

    # Mark nginx outgoing traffic for bypass routing (for upstream connections)
    iptables -t mangle -A OUTPUT -m owner --uid-owner nginx -j MARK --set-mark 200 2>/dev/null || true

    # Route marked packets through bypass table
    ip rule add fwmark 200 table nginx_bypass priority 200 2>/dev/null || true

    log_success "Nginx routing configured for VPN bypass (local IP: $LOCAL_IP)"
}

# Cleanup function
cleanup_nginx_routing() {
    log_info "Cleaning up nginx routing..."

    # Get local IP for cleanup
    LOCAL_IP=$(ip route get ******* | grep -oP 'src \K\S+' | head -1 2>/dev/null)

    # Remove iptables rules
    iptables -t mangle -D OUTPUT -m owner --uid-owner nginx -j MARK --set-mark 200 2>/dev/null || true

    # Remove routing rules
    ip rule del fwmark 200 table nginx_bypass 2>/dev/null || true
    if [[ -n "$LOCAL_IP" ]]; then
        ip rule del from "$LOCAL_IP" table nginx_bypass 2>/dev/null || true
    fi

    # Clear custom routing table
    ip route flush table nginx_bypass 2>/dev/null || true

    log_success "Nginx routing cleanup completed"
}

# Main execution
case "${1:-start}" in
    start)
        if ! detect_physical_network; then
            log_error "Failed to detect network configuration"
            exit 1
        fi
        setup_nginx_routing
        
        # Start nginx normally (not in namespace)
        log_info "Starting nginx..."
        /usr/sbin/nginx
        ;;
    stop)
        log_info "Stopping nginx..."
        /usr/sbin/nginx -s quit 2>/dev/null || true
        cleanup_nginx_routing
        ;;
    reload)
        log_info "Reloading nginx..."
        /usr/sbin/nginx -s reload
        ;;
    test)
        log_info "Testing nginx configuration..."
        /usr/sbin/nginx -t
        ;;
    cleanup)
        cleanup_nginx_routing
        ;;
    *)
        echo "Usage: $0 {start|stop|reload|test|cleanup}"
        exit 1
        ;;
esac
