# Universal Amnezia VPN Bypass

A powerful tool that allows **ANY application** to bypass Amnezia VPN while maintaining VPN protection for all other system traffic.

## 🚀 Features

- **Universal Compatibility**: Works with ANY application (browsers, games, CLI tools, scripts)
- **Full Audio Support**: Applications have complete audio functionality through main system speakers
- **Concurrent Operation**: VPN and direct internet access work simultaneously
- **Safe**: Never modifies or breaks existing VPN routes
- **Network Namespaces**: Uses Linux network namespaces for complete isolation
- **Policy-Based Routing**: Advanced routing ensures proper traffic separation
- **GUI Application Support**: Full support for graphical applications with audio and display

## 📋 Requirements

- Linux system with root access
- Amnezia VPN (AmneziaWG) running
- `iptables`, `ip` command tools (usually pre-installed)

## 🔧 Installation

1. Clone or download this repository
2. Make the script executable:
   ```bash
   chmod +x concurrent_bypass_final.sh
   chmod +x bypass-vpn
   ```

## 📖 Usage

### Quick Start
```bash
# Check your real IP (bypassing VPN)
sudo ./concurrent_bypass_final.sh curl ifconfig.me

# Launch Firefox with direct internet access
sudo ./concurrent_bypass_final.sh firefox

# Run any Python script without VPN
sudo ./concurrent_bypass_final.sh python3 my_script.py
```

### Using the Wrapper Script
```bash
# Simpler usage with the wrapper (uses pkexec for GUI authentication)
./bypass-vpn curl ifconfig.me
./bypass-vpn firefox
./bypass-vpn discord
```

### Authentication Methods

The script supports both `sudo` and `pkexec` for privilege escalation:

```bash
# Method 1: Using sudo (traditional)
sudo ./concurrent_bypass_final.sh <command>

# Method 2: Using pkexec (GUI authentication dialog)
pkexec ./concurrent_bypass_final.sh <command>

# Method 3: Using wrapper script (automatically uses pkexec)
./bypass-vpn <command>
```

**Note**: The wrapper script `bypass-vpn` now uses `pkexec` by default, which provides a graphical authentication dialog instead of requiring terminal sudo access.

## 🔍 How It Works

1. **Network Namespace**: Creates isolated network environment
2. **Veth Pair**: Connects namespace to main system (********** ↔ **********)
3. **Policy-Based Routing**: Uses custom "direct" routing table for namespace traffic
4. **NAT**: Translates namespace traffic through physical interface
5. **Audio Passthrough**: Bind mounts PulseAudio/PipeWire sockets for seamless audio
6. **X11 Support**: Shares display server for GUI applications
7. **DNS**: Configured for local DNS resolution

## ✅ Verification

Compare IPs to verify bypass is working:
```bash
# Main system (should show VPN IP)
curl ifconfig.me

# Bypass (should show real ISP IP)
sudo ./concurrent_bypass_final.sh curl ifconfig.me
```

## 🛡️ Safety

- **VPN routes preserved**: Never modifies `0.0.0.0/1` and `*********/1` routes
- **Automatic cleanup**: Removes all temporary network configurations on exit
- **No interference**: Main system VPN functionality remains untouched

## 🎯 Use Cases

- **Gaming**: Play games with direct connection and full audio while keeping other traffic private
- **Streaming**: Access geo-restricted content with audio/video while maintaining privacy
- **Media Applications**: Run audio/video players, music software with direct internet access
- **Development**: Test applications with real IP and audio functionality while staying protected
- **Downloads**: Faster downloads for specific applications
- **Communication**: Use Discord, Skype, etc. with direct connection and full audio support

## 🔧 Technical Details

- Uses Linux network namespaces for complete traffic isolation
- Implements policy-based routing with custom routing tables
- Employs NAT for seamless internet access from namespace
- Bind mounts PulseAudio/PipeWire sockets for audio passthrough
- Shares X11 display server for GUI application support
- Preserves user environment and permissions within namespace
- Supports any application without requiring special interface binding

---

**Note**: This tool is designed specifically for Amnezia VPN (AmneziaWG) and requires root privileges for network namespace management.
