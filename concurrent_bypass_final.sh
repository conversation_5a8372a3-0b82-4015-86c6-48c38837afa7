#!/bin/bash

# Universal Amnezia VPN Bypass - Network Namespace Solution
# Allows ANY application to bypass VPN while maintaining VPN for all other traffic
# Uses network namespaces with policy-based routing for universal compatibility

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the original user (works with both sudo and pkexec)
get_original_user() {
    # Try different methods to get the original user
    if [[ -n "$SUDO_USER" ]]; then
        echo "$SUDO_USER"
    elif [[ -n "$PKEXEC_UID" ]]; then
        getent passwd "$PKEXEC_UID" | cut -d: -f1
    else
        # Fallback: get the user who owns the terminal
        who am i | awk '{print $1}' | head -1
    fi
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo or pkexec)"
        exit 1
    fi

    # Set the original user
    ORIGINAL_USER=$(get_original_user)
    if [[ -z "$ORIGINAL_USER" ]]; then
        log_error "Could not determine original user"
        exit 1
    fi

    log_info "Running as root, original user: $ORIGINAL_USER"
}

# Check VPN status
check_vpn_status() {
    if ! ip link show amn0 &>/dev/null; then
        log_warning "Amnezia VPN interface 'amn0' not found"
        log_info "This script is designed to work alongside Amnezia VPN"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "Amnezia VPN is active (amn0 interface found)"
    fi
}

# Run a command with VPN bypass using careful route manipulation
run_bypass_command() {
    local app_command="$*"

    if [[ -z "$app_command" ]]; then
        log_error "No command provided to run_bypass_command"
        return 1
    fi

    # Create a script that temporarily modifies routes for this specific process
    local bypass_script="/tmp/bypass_runner_$$.sh"

    cat > "$bypass_script" << 'EOF'
#!/bin/bash

# Save current VPN routes with exact parameters
VPN_ROUTES=$(ip route show | grep "dev amn0")

# Function to restore VPN routes
restore_vpn() {
    if [[ -n "$VPN_ROUTES" ]]; then
        while IFS= read -r route; do
            if [[ -n "$route" ]]; then
                ip route add $route 2>/dev/null || true
            fi
        done <<< "$VPN_ROUTES"
    fi
}

# Set trap to restore routes on exit
trap restore_vpn EXIT

# Temporarily remove VPN routes for this process
# Use exact route specifications to ensure proper removal
ip route del 0.0.0.0/1 dev amn0 scope link metric 1 2>/dev/null || true
ip route del *********/1 dev amn0 scope link metric 1 2>/dev/null || true

# Run the command with direct internet access
"$@"

# Routes will be restored by the trap
EOF

    chmod +x "$bypass_script"

    # Run the bypass script
    "$bypass_script" "$@"
    local exit_code=$?

    # Clean up
    rm -f "$bypass_script"

    return $exit_code
}

# Test concurrent access
test_concurrent_access() {
    log_info "Testing concurrent VPN and direct access..."

    # Test main system (should use VPN)
    log_info "Testing main system IP (should be VPN)..."
    MAIN_IP=$(curl -s --connect-timeout 10 ifconfig.me 2>/dev/null || echo "Failed")
    log_info "Main system IP: $MAIN_IP"

    # Test bypass (should use direct internet)
    log_info "Testing bypass IP (should be direct)..."
    BYPASS_IP=$(run_bypass_command curl -s --connect-timeout 10 ifconfig.me 2>/dev/null || echo "Failed")
    log_info "Bypass IP: $BYPASS_IP"

    if [[ "$BYPASS_IP" != "$MAIN_IP" && "$BYPASS_IP" != "Failed" && "$MAIN_IP" != "Failed" ]]; then
        log_success "SUCCESS: Concurrent access working!"
        log_success "  Main system (VPN): $MAIN_IP"
        log_success "  Bypass (direct): $BYPASS_IP"
        return 0
    else
        log_error "Concurrent access test failed"
        log_error "  Main system IP: $MAIN_IP"
        log_error "  Bypass IP: $BYPASS_IP"
        return 1
    fi
}

# Detect physical network configuration
detect_physical_network() {
    # Find the route to VPN server (this shows the original routing)
    VPN_SERVER_ROUTE=$(ip route show | grep "************" | head -1)
    if [[ -n "$VPN_SERVER_ROUTE" ]]; then
        PHYSICAL_IF=$(echo "$VPN_SERVER_ROUTE" | awk '{print $5}')
        PHYSICAL_GW=$(echo "$VPN_SERVER_ROUTE" | awk '{print $3}')
    else
        # Fallback: use the interface with lowest metric default route (excluding VPN)
        DEFAULT_ROUTE=$(ip route show | grep "default" | grep -v "amn0" | sort -k9 -n | head -1)
        PHYSICAL_IF=$(echo "$DEFAULT_ROUTE" | awk '{print $5}')
        PHYSICAL_GW=$(echo "$DEFAULT_ROUTE" | awk '{print $3}')
    fi

    if [[ -z "$PHYSICAL_IF" || -z "$PHYSICAL_GW" ]]; then
        log_error "Could not detect physical interface and gateway"
        return 1
    fi

    # Get the physical interface IP
    PHYSICAL_IP=$(ip addr show "$PHYSICAL_IF" | grep "inet " | awk '{print $2}' | cut -d'/' -f1)

    if [[ -z "$PHYSICAL_IP" ]]; then
        log_error "Could not detect physical IP"
        return 1
    fi

    log_info "Physical network: $PHYSICAL_IF ($PHYSICAL_IP) via $PHYSICAL_GW"
    return 0
}

# Global variables for cleanup
CURRENT_NS_NAME=""
CURRENT_VETH_HOST=""
CURRENT_NS_SUBNET=""

# Global cleanup function
cleanup_namespace_global() {
    if [[ -n "$CURRENT_NS_NAME" ]]; then
        log_info "Cleaning up namespace..."
        # Unmount audio and X11 sockets
        umount "/tmp/.X11-unix" 2>/dev/null || true
        umount "/run/user/$(id -u $ORIGINAL_USER)/pulse" 2>/dev/null || true
        # Clean up network namespace
        ip netns delete "$CURRENT_NS_NAME" 2>/dev/null || true
        ip link delete "$CURRENT_VETH_HOST" 2>/dev/null || true
        iptables -t nat -D POSTROUTING -s "$CURRENT_NS_SUBNET" -o "$PHYSICAL_IF" -j MASQUERADE 2>/dev/null || true
        iptables -D FORWARD -i "$CURRENT_VETH_HOST" -o "$PHYSICAL_IF" -j ACCEPT 2>/dev/null || true
        iptables -D FORWARD -i "$PHYSICAL_IF" -o "$CURRENT_VETH_HOST" -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
        ip rule del from "$CURRENT_NS_SUBNET" table direct 2>/dev/null || true
        rm -rf "/etc/netns/$CURRENT_NS_NAME" 2>/dev/null || true
        # Reset global variables
        CURRENT_NS_NAME=""
        CURRENT_VETH_HOST=""
        CURRENT_NS_SUBNET=""
    fi
}

# Note: cleanup is called explicitly at the end of run_bypass_command

# Run a command with VPN bypass using network namespace
run_bypass_command() {
    local app_command="$*"

    if [[ -z "$app_command" ]]; then
        log_error "No command provided to run_bypass_command"
        return 1
    fi

    # Detect physical network configuration
    if ! detect_physical_network; then
        log_error "Failed to detect physical network configuration"
        return 1
    fi

    # Create a unique namespace name
    local ns_name="bypass_$$"
    local veth_host="veth_host"
    local veth_ns="veth_ns"
    local ns_ip="**********"
    local host_ip="**********"
    local ns_subnet="**********/24"

    # Set global variables for cleanup
    CURRENT_NS_NAME="$ns_name"
    CURRENT_VETH_HOST="$veth_host"
    CURRENT_NS_SUBNET="$ns_subnet"

    # Local cleanup function with proper variables
    cleanup_local() {
        log_info "Cleaning up namespace..."
        # Unmount audio and X11 sockets
        umount "/tmp/.X11-unix" 2>/dev/null || true
        umount "/run/user/$(id -u $ORIGINAL_USER)/pulse" 2>/dev/null || true
        # Clean up network namespace
        ip netns delete "$ns_name" 2>/dev/null || true
        ip link delete "$veth_host" 2>/dev/null || true
        iptables -t nat -D POSTROUTING -s "$ns_subnet" -o "$PHYSICAL_IF" -j MASQUERADE 2>/dev/null || true
        iptables -D FORWARD -i "$veth_host" -o "$PHYSICAL_IF" -j ACCEPT 2>/dev/null || true
        iptables -D FORWARD -i "$PHYSICAL_IF" -o "$veth_host" -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true
        ip rule del from "$ns_subnet" table direct 2>/dev/null || true
        rm -rf "/etc/netns/$ns_name" 2>/dev/null || true
    }

    log_info "Setting up bypass namespace..."

    # Clean up any existing interfaces first
    ip link delete "$veth_host" 2>/dev/null || true
    ip netns delete "$ns_name" 2>/dev/null || true

    # Create network namespace
    ip netns add "$ns_name" || {
        log_error "Failed to create namespace"
        return 1
    }

    # Create veth pair
    ip link add "$veth_host" type veth peer name "$veth_ns" || {
        log_error "Failed to create veth pair"
        return 1
    }

    # Move one end to namespace
    ip link set "$veth_ns" netns "$ns_name" || {
        log_error "Failed to move veth to namespace"
        return 1
    }

    # Configure host end
    ip addr add "$host_ip/24" dev "$veth_host" || {
        log_error "Failed to configure host veth"
        return 1
    }
    ip link set "$veth_host" up || {
        log_error "Failed to bring up host veth"
        return 1
    }

    # Configure namespace end
    ip netns exec "$ns_name" ip addr add "$ns_ip/24" dev "$veth_ns" || {
        log_error "Failed to configure namespace veth"
        return 1
    }
    ip netns exec "$ns_name" ip link set "$veth_ns" up || {
        log_error "Failed to bring up namespace veth"
        return 1
    }
    ip netns exec "$ns_name" ip link set lo up || {
        log_error "Failed to bring up namespace loopback"
        return 1
    }

    # Set default route in namespace
    ip netns exec "$ns_name" ip route add default via "$host_ip" || {
        log_error "Failed to set namespace default route"
        return 1
    }

    # Enable IP forwarding
    echo 1 > /proc/sys/net/ipv4/ip_forward

    # Set up direct routing table if it doesn't exist
    if ! grep -q "direct" /etc/iproute2/rt_tables; then
        echo "200 direct" >> /etc/iproute2/rt_tables
    fi

    # Add routes to direct table
    ip route add default via "$PHYSICAL_GW" dev "$PHYSICAL_IF" table direct 2>/dev/null || true
    ip route add ***********/24 dev "$PHYSICAL_IF" table direct 2>/dev/null || true

    # Add policy rule for namespace traffic
    ip rule add from "$ns_subnet" table direct || {
        log_error "Failed to add policy rule"
        return 1
    }

    # Set up NAT for namespace traffic
    iptables -t nat -A POSTROUTING -s "$ns_subnet" -o "$PHYSICAL_IF" -j MASQUERADE || {
        log_error "Failed to add NAT rule"
        return 1
    }

    # Allow forwarding
    iptables -A FORWARD -i "$veth_host" -o "$PHYSICAL_IF" -j ACCEPT || {
        log_error "Failed to add forward rule"
        return 1
    }
    iptables -A FORWARD -i "$PHYSICAL_IF" -o "$veth_host" -m state --state RELATED,ESTABLISHED -j ACCEPT || {
        log_error "Failed to add return forward rule"
        return 1
    }

    # Configure DNS in namespace
    mkdir -p "/etc/netns/$ns_name"
    echo "nameserver ***********" > "/etc/netns/$ns_name/resolv.conf"

    # Set up audio passthrough
    log_info "Setting up audio passthrough..."

    # Create audio runtime directory in namespace
    ip netns exec "$ns_name" mkdir -p "/run/user/$(id -u $ORIGINAL_USER)/pulse" || {
        log_error "Failed to create audio runtime directory"
        return 1
    }

    # Bind mount PulseAudio socket for audio access
    mount --bind "/run/user/$(id -u $ORIGINAL_USER)/pulse" "/run/user/$(id -u $ORIGINAL_USER)/pulse" || {
        log_error "Failed to bind mount audio socket"
        return 1
    }

    # Set up X11 socket for GUI applications
    ip netns exec "$ns_name" mkdir -p "/tmp/.X11-unix" || {
        log_error "Failed to create X11 directory"
        return 1
    }
    mount --bind "/tmp/.X11-unix" "/tmp/.X11-unix" || {
        log_error "Failed to bind mount X11 socket"
        return 1
    }

    log_info "Namespace ready with audio support. Running command: $app_command"

    # Run the command in the namespace as the original user with full environment
    ip netns exec "$ns_name" sudo -u "$ORIGINAL_USER" env \
        XDG_RUNTIME_DIR="/run/user/$(id -u $ORIGINAL_USER)" \
        PULSE_RUNTIME_PATH="/run/user/$(id -u $ORIGINAL_USER)/pulse" \
        DISPLAY="${DISPLAY:-:0}" \
        HOME="/home/<USER>" \
        USER="$ORIGINAL_USER" \
        "$@"
    local exit_code=$?

    # Explicit cleanup call
    cleanup_local
    return $exit_code
}

# Launch application with bypass
launch_app() {
    local app_command="$*"

    if [[ -z "$app_command" ]]; then
        log_error "No application command provided"
        echo ""
        echo "Usage: $0 <command> [args...]"
        echo ""
        echo "Examples:"
        echo "  $0 curl ifconfig.me"
        echo "  $0 wget https://example.com/file.zip"
        echo "  $0 firefox"
        echo "  $0 ping -c 3 google.com"
        echo ""
        exit 1
    fi

    log_info "Launching application with VPN bypass: $app_command"

    # Run the command with bypass (already running as root)
    run_bypass_command "$@"
}

# Show status
show_status() {
    log_info "Current network status:"
    
    # Show VPN interface
    if ip link show amn0 &>/dev/null; then
        VPN_IP=$(ip addr show amn0 | grep "inet " | awk '{print $2}' | cut -d'/' -f1)
        log_info "  VPN interface: amn0 ($VPN_IP)"
    else
        log_warning "  VPN interface: Not found"
    fi
    
    # Show VPN routes
    VPN_ROUTES=$(ip route show | grep "dev amn0" | wc -l)
    log_info "  VPN routes: $VPN_ROUTES active"
    
    # Show external IP
    EXTERNAL_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || echo "Failed")
    log_info "  External IP: $EXTERNAL_IP"
    
    # Test if bypass would work
    log_info ""
    log_info "Testing bypass functionality..."
    if test_concurrent_access; then
        log_success "Bypass is working correctly!"
    else
        log_error "Bypass test failed!"
    fi
}

# Main execution
main() {
    check_root
    check_vpn_status
    
    if [[ $# -eq 0 ]]; then
        echo ""
        echo "=== Universal Amnezia VPN Bypass ==="
        echo ""
        echo "This script allows ANY application to bypass the VPN using network namespaces"
        echo "while maintaining VPN protection for all other traffic."
        echo ""

        show_status

        echo ""
        echo "Usage: $0 <command> [args...]"
        echo ""
        echo "Examples:"
        echo "  $0 curl ifconfig.me          # Check real IP"
        echo "  $0 wget https://file.zip     # Download without VPN"
        echo "  $0 firefox                   # Browse with direct connection"
        echo "  $0 discord                   # Chat without VPN"
        echo "  $0 steam                     # Gaming with direct connection"
        echo "  $0 python3 script.py        # Run any script without VPN"
        echo ""
        echo "Works with ANY application! VPN remains active for all other traffic."
        echo ""
        exit 0
    fi
    
    launch_app "$@"
}

# Handle command line arguments
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
